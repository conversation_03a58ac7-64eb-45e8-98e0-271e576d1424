# MyOS - 最小化教学操作系统

这是一个基于xv6风格的最小化教学操作系统，能够在控制台输出"Hello World"。

## 项目结构

```
/myos
|-- boot/              # 引导加载相关文件
|   |-- bootasm.S      # 引导汇编代码
|   |-- bootmain.c     # 引导C代码
|   `-- sign.pl        # 引导扇区签名工具
|
|-- include/           # 头文件目录
|   |-- asm.h          # 汇编相关定义
|   |-- console.h      # 控制台相关定义
|   |-- defs.h         # 函数声明
|   |-- elf.h          # ELF格式定义
|   |-- memlayout.h    # 内存布局定义
|   |-- mmu.h          # 内存管理单元定义
|   |-- types.h        # 基本类型定义
|   `-- x86.h          # x86架构相关定义
|
|-- kernel/            # 内核核心文件
|   |-- entry.S        # 内核入口点
|   `-- main.c         # 内核主函数
|
|-- drivers/           # 设备驱动文件
|   `-- console.c      # 控制台驱动
|
|-- scripts/           # 构建脚本
|   `-- kernel.ld      # 内核链接脚本
|
|-- build/             # 编译生成的文件
|-- Makefile           # 主Makefile
`-- README.md          # 项目说明文件
```

## 系统功能

1. 引导加载程序 - 从实模式切换到保护模式，加载内核
2. 内存管理 - 简单的页表设置
3. 控制台输出 - 能够在屏幕上显示文本

## 编译和运行

### 依赖项

确保你的系统上安装了以下工具：

- GCC (支持32位编译)
- QEMU
- Make
- Perl

在Ubuntu/Debian上，可以使用以下命令安装依赖：

```bash
sudo apt-get update
sudo apt-get install gcc-multilib qemu-system-x86 make perl
```

### 编译

在项目根目录下运行：

```bash
make
```

这将生成`myos.img`磁盘镜像文件和`build/kernel`可执行文件。

### 运行

使用QEMU运行操作系统：

```bash
make qemu
```

### 调试

使用GDB调试操作系统：

```bash
make qemu-gdb
```

然后在另一个终端中启动GDB：

```bash
gdb -ex "target remote localhost:26000" -ex "symbol-file build/kernel"
```

## 系统设计

### 引导过程

1. BIOS加载引导扇区(bootblock)到内存地址0x7C00
2. 引导程序从实模式切换到保护模式
3. 引导程序加载内核ELF文件到内存
4. 控制权转移到内核入口点

### 内存布局

- 0x00000000 - 0x00100000: 保留区域(包括BIOS、显存等)
- 0x00100000: 内核加载地址(物理地址)
- 0x80000000: 内核虚拟地址起始位置

### 内核初始化

1. 设置页表
2. 初始化控制台
3. 输出"Hello World"消息

## 扩展方向

1. 实现中断处理
2. 添加进程管理
3. 实现文件系统
4. 添加用户空间支持
5. 实现系统调用

#include "../include/types.h"
#include "../include/defs.h"
#include "../include/proc.h"
#include "../include/memlayout.h"
#include "../include/x86.h"

// 全局进程表
struct proc procs[MAXPROGS];
struct proc *current_proc = 0;
static uint next_pid = 1;

// 异常处理机制
static int exception_occurred = 0;
static jmp_buf exception_jmp;

// 初始化进程管理
void proc_init(void)
{
    int i;
    for (i = 0; i < MAXPROGS; i++)
    {
        procs[i].state = UNUSED;
        procs[i].pid = 0;
    }
    cprintf("Process management initialized\n");
}

// 分配一个新进程
struct proc *proc_alloc(void)
{
    int i;
    for (i = 0; i < MAXPROGS; i++)
    {
        if (procs[i].state == UNUSED)
        {
            procs[i].state = EMBRYO;
            procs[i].pid = next_pid++;
            procs[i].stack_base = USTACKBASE;
            procs[i].stack_size = USTACKSIZE;
            return &procs[i];
        }
    }
    return 0; // 没有可用的进程槽
}

// 释放进程
void proc_free(struct proc *p)
{
    if (p == 0)
        return;

    p->state = UNUSED;
    p->pid = 0;
    p->entry = 0;
    p->size = 0;
}

// 加载程序到进程（使用ELF加载器）
int proc_load(struct proc *p, void *binary, uint size)
{
    if (p == 0 || binary == 0 || size == 0)
        return -1;

    if (size > PROGSIZE)
        return -1; // 程序太大

    // 使用ELF加载器
    if (elf_load(p, binary, size) < 0)
    {
        cprintf("Failed to load ELF binary\n");
        return -1;
    }

    // 初始化用户栈
    p->context.esp = p->stack_base + p->stack_size - 4; // 栈顶
    p->context.eip = p->entry;                          // 程序入口
    p->context.eflags = 0x202;                          // 启用中断

    cprintf("Process %d loaded at 0x%x, size %d bytes\n", p->pid, p->entry, size);
    return 0;
}

// 用户态切换函数（汇编实现）
extern void switch_to_user(uint entry, uint stack);

// 运行进程
void proc_run(struct proc *p)
{
    if (p == 0 || p->state != RUNNABLE)
    {
        cprintf("Cannot run process: invalid state\n");
        return;
    }

    current_proc = p;
    p->state = RUNNING;

    cprintf("Running process %d (%s) at 0x%x\n", p->pid, p->name, p->entry);
    cprintf("Stack at 0x%x, size %d bytes\n", p->stack_base, p->stack_size);

    // 设置用户栈
    uint user_stack = p->stack_base + p->stack_size - 4;

    // 在栈上放置返回地址（指向exit系统调用）
    *((uint *)user_stack) = 0; // 简化：直接放0作为返回地址

    cprintf("Switching to user mode...\n");

    // 切换到用户态执行
    // 这里使用简化的方法：直接调用用户程序，但添加异常处理
    void (*user_func)(void) = (void (*)(void))p->entry;

    if (user_func)
    {
        cprintf("About to call user function at 0x%x\n", (uint)user_func);

        // 设置进程状态为运行中
        p->state = RUNNING;
        exception_occurred = 0;

        // 设置异常跳转点
        if (setjmp(exception_jmp) == 0)
        {
            // 第一次调用 setjmp，执行用户程序
            // 注意：用户程序可能会调用exit()系统调用，这会设置current_proc = 0
            user_func();
            cprintf("User function returned normally\n");
        }
        else
        {
            // 从 longjmp 返回，说明发生了异常
            cprintf("User function terminated due to exception\n");
        }
    }
    else
    {
        cprintf("Invalid user program entry point\n");
        proc_exit(-1);
        return;
    }

    // 检查进程是否通过系统调用退出
    if (current_proc == 0)
    {
        cprintf("Process %d (%s) exited via system call\n", p->pid, p->name);
        p->state = ZOMBIE;
    }
    else if (current_proc == p)
    {
        cprintf("Process %d completed without explicit exit\n", p->pid);
        current_proc->state = ZOMBIE;
        current_proc = 0;
    }

    cprintf("Process %d (%s) execution completed, returning from proc_run\n", p->pid, p->name);
}

// 进程退出
void proc_exit(int status)
{
    if (current_proc == 0)
        return;

    cprintf("Process %d (%s) exited with status %d\n",
            current_proc->pid, current_proc->name, status);

    current_proc->state = ZOMBIE;
    current_proc = 0;

    // 如果是异常退出，使用 longjmp 跳出用户程序执行
    if (status < 0)
    {
        exception_occurred = 1;
        longjmp(exception_jmp, 1);
    }
}

# setjmp/longjmp 实现

.text

# int setjmp(jmp_buf env)
.globl setjmp
setjmp:
    movl 4(%esp), %eax      # 获取 env 参数
    movl %ebx, 0(%eax)      # 保存 ebx
    movl %esp, 4(%eax)      # 保存 esp
    movl %ebp, 8(%eax)      # 保存 ebp
    movl %esi, 12(%eax)     # 保存 esi
    movl %edi, 16(%eax)     # 保存 edi
    movl (%esp), %edx       # 获取返回地址
    movl %edx, 20(%eax)     # 保存 eip
    xorl %eax, %eax         # 返回 0
    ret

# void longjmp(jmp_buf env, int val)
.globl longjmp
longjmp:
    movl 4(%esp), %eax      # 获取 env 参数
    movl 8(%esp), %edx      # 获取 val 参数
    movl 0(%eax), %ebx      # 恢复 ebx
    movl 4(%eax), %esp      # 恢复 esp
    movl 8(%eax), %ebp      # 恢复 ebp
    movl 12(%eax), %esi     # 恢复 esi
    movl 16(%eax), %edi     # 恢复 edi
    movl 20(%eax), %ecx     # 获取 eip
    movl %ecx, (%esp)       # 设置返回地址
    movl %edx, %eax         # 设置返回值
    testl %eax, %eax        # 如果 val 为 0
    jnz 1f
    incl %eax               # 则返回 1
1:
    ret

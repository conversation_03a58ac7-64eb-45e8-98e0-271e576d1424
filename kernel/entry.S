# 内核入口点
# Multiboot 头
.set ALIGN,    1<<0             # 页面对齐
.set MEMINFO,  1<<1             # 提供内存映射
.set FLAGS,    ALIGN | MEMINFO  # 这是multiboot 'flag' 字段
.set MAGIC,    0x1BADB002       # 'magic number'
.set CHECKSUM, -(MAGIC + FLAGS) # 校验和

# Multiboot 头
.section .multiboot
.align 4
.long MAGIC
.long FLAGS
.long CHECKSUM

# 内核栈
.section .bootstrap_stack, "aw", @nobits
stack_bottom:
.skip 16384 # 16 KiB
stack_top:

# 内核入口点
.section .text
.global _start
.type _start, @function
_start:
  # 设置栈指针
  movl $stack_top, %esp

  # 跳转到main()
  call kmain

  # 如果返回，进入无限循环
  cli
1:  hlt
  jmp 1b

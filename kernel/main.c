#include "../include/types.h"
#include "../include/defs.h"
#include "../include/x86.h"
#include "../include/uart.h"
#include "../include/proc.h"
#include "../include/trap.h"
#include "../include/syscall.h"

// 内核主函数
void kmain(void)
{
  // 初始化控制台
  consoleinit();

  // 输出启动信息
  cprintf("MyOS - Mini Operating System with Batch Processing\n");
  cprintf("================================================\n");

  // 初始化串行端口
  uartinit();

  // 初始化进程管理
  proc_init();

  // 初始化系统调用
  syscall_init();

  // 初始化中断处理
  trap_init();

  // 初始化批处理系统
  cprintf("About to initialize batch system...\n");
  batch_init();
  cprintf("Batch system initialized successfully.\n");

  // 暂时不启用中断，先测试用户程序加载
  cprintf("Skipping interrupt enable for now...\n");
  // sti();
  cprintf("Continuing without interrupts.\n");

  cprintf("\nKernel initialization completed!\n");
  cprintf("Starting batch processing system...\n");

  // 运行批处理系统
  batch_run();

  cprintf("\nAll programs completed. System halting...\n");

  // 系统完成，进入无限循环
  for (;;)
  {
    // 可以在这里添加简单的shell或其他交互功能
    uartpoll();
  }
}

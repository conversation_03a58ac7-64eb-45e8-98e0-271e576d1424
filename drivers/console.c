#include "../include/types.h"
#include "../include/defs.h"
#include "../include/console.h"
#include "../include/x86.h"

// 函数声明已在defs.h中

// 串行端口定义
#define COM1 0x3f8

// 初始化控制台串行端口
static void
console_uartinit(void)
{
  // 关闭中断
  outb(COM1 + 1, 0x00);

  // 设置波特率
  outb(COM1 + 3, 0x80); // 解锁除数寄存器
  outb(COM1 + 0, 115200 / 9600);
  outb(COM1 + 1, 0);

  // 8位数据位，无奇偶校验，1位停止位
  outb(COM1 + 3, 0x03);

  // 启用FIFO，清除接收和发送FIFO
  outb(COM1 + 2, 0xC7);

  // 启用中断，RTS/DSR设置
  outb(COM1 + 4, 0x0B);
}

// 向控制台串行端口输出一个字符
static void
console_uartputc(int c)
{
  // 等待发送缓冲区为空
  for (int i = 0; i < 128 && !(inb(COM1 + 5) & 0x20); i++)
    ;

  outb(COM1 + 0, c);
}

// 初始化控制台
void consoleinit(void)
{
  // 初始化控制台串行端口
  console_uartinit();
}

// 输出一个字符到控制台
void consputc(int c)
{
  // 输出到串行端口
  if (c == '\n')
    console_uartputc('\r'); // 添加回车符
  console_uartputc(c);
}

// 简单的printf实现
void cprintf(char *fmt, ...)
{
  char *s;
  int c, i;
  uint *argp;

  argp = (uint *)(void *)(&fmt + 1);
  for (i = 0; (c = fmt[i] & 0xff) != 0; i++)
  {
    if (c != '%')
    {
      consputc(c);
      continue;
    }
    c = fmt[++i] & 0xff;
    if (c == 0)
      break;
    switch (c)
    {
    case 'd':
      // 简单的整数转字符串
      {
        int num = argp[0];
        argp++;

        if (num < 0)
        {
          consputc('-');
          num = -num;
        }

        // 处理0的特殊情况
        if (num == 0)
        {
          consputc('0');
          break;
        }

        // 将数字转换为字符串并输出
        char digits[10];
        int idx = 0;
        while (num > 0)
        {
          digits[idx++] = '0' + (num % 10);
          num /= 10;
        }

        while (--idx >= 0)
          consputc(digits[idx]);
      }
      break;
    case 'x':
    case 'p':
      // 十六进制输出
      {
        uint num = argp[0];
        argp++;

        // 处理0的特殊情况
        if (num == 0)
        {
          consputc('0');
          break;
        }

        // 将数字转换为十六进制字符串并输出
        int i;
        char digits[8];
        for (i = 0; i < 8 && num > 0; i++)
        {
          int digit = num % 16;
          if (digit < 10)
            digits[i] = '0' + digit;
          else
            digits[i] = 'a' + (digit - 10);
          num /= 16;
        }

        while (--i >= 0)
          consputc(digits[i]);
      }
      break;
    case 's':
      if ((s = (char *)*argp++) == 0)
        s = "(null)";
      for (; *s; s++)
        consputc(*s);
      break;
    case '%':
      consputc('%');
      break;
    default:
      // 未知格式，直接输出
      consputc('%');
      consputc(c);
      break;
    }
  }
}

// 内核panic函数
void panic(char *s)
{
  cli(); // 禁用中断
  cprintf("kernel panic: %s\n", s);
  for (;;)
    ; // 无限循环
}

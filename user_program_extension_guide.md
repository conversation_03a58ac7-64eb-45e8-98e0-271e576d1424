# MyOS 用户程序扩展快速指南

## 概述

本指南提供了为 MyOS 批处理系统添加新用户程序的简化步骤说明。

## 快速步骤

### 1. 创建用户程序源文件

在 `user/` 目录下创建新的 C 源文件：

```c
// user/newprogram.c
#include "user.h"

int main(void)
{
    printf("Hello from New Program!\n");
    
    int pid = getpid();
    printf("PID: ");
    char pid_str[10];
    itoa(pid, pid_str);
    printf(pid_str);
    printf("\n");
    
    // 你的程序逻辑
    printf("Program logic here...\n");
    
    printf("Program completed!\n");
    exit(0);
    return 0;
}
```

### 2. 修改 Makefile

在 `Makefile` 中的 `USER_SRCS` 变量中添加新程序：

```makefile
# 原来的
USER_SRCS = $(USER_DIR)/error.c $(USER_DIR)/hello.c $(USER_DIR)/fibonacci.c $(USER_DIR)/count.c $(USER_DIR)/test.c

# 修改后（添加 newprogram.c）
USER_SRCS = $(USER_DIR)/error.c $(USER_DIR)/hello.c $(USER_DIR)/fibonacci.c $(USER_DIR)/count.c $(USER_DIR)/test.c $(USER_DIR)/newprogram.c
```

**注意**：程序在批处理中的执行顺序由 `USER_SRCS` 中的顺序决定。

### 3. 编译和测试

```bash
make clean && make && make qemu
```

## 实际示例：fibonacci 程序

我们已经成功添加了一个 fibonacci 程序作为示例：

```c
#include "user.h"

int fibonacci(int n)
{
    if (n <= 1) return n;
    return fibonacci(n-1) + fibonacci(n-2);
}

int main(void)
{
    printf("Fibonacci Calculator Program\n");
    
    int pid = getpid();
    printf("PID: ");
    char pid_str[10];
    itoa(pid, pid_str);
    printf(pid_str);
    printf("\n");
    
    printf("Calculating Fibonacci sequence:\n");
    
    for (int i = 0; i < 10; i++) {
        int fib = fibonacci(i);
        printf("F(");
        char i_str[10];
        itoa(i, i_str);
        printf(i_str);
        printf(") = ");
        char fib_str[10];
        itoa(fib, fib_str);
        printf(fib_str);
        printf("\n");
    }
    
    printf("Fibonacci program completed!\n");
    exit(0);
    return 0;
}
```

## 当前执行顺序

修改后的程序执行顺序为：
1. **error** - 异常处理测试程序
2. **hello** - 简单问候程序
3. **fibonacci** - 斐波那契数列计算程序
4. **count** - 计数程序
5. **test** - 系统调用测试程序

## 可用的库函数

用户程序可以使用以下库函数：

- `printf(char *fmt, ...)` - 格式化输出
- `getpid()` - 获取进程ID
- `exit(int status)` - 程序退出
- `strlen(char *s)` - 字符串长度
- `strcpy(char *dst, char *src)` - 字符串复制
- `itoa(int num, char *str)` - 整数转字符串

## 系统调用

可用的系统调用：
- `SYS_exit` (1) - 程序退出
- `SYS_write` (2) - 写入数据
- `SYS_read` (3) - 读取数据
- `SYS_getpid` (4) - 获取进程ID

## 注意事项

1. **必须包含** `#include "user.h"`
2. **必须有** `main()` 函数作为入口点
3. **必须调用** `exit(0)` 来正常退出程序
4. **程序大小限制**：每个程序最大 128KB
5. **程序数量限制**：最多 8 个程序

## 验证结果

成功添加程序后，系统启动时会显示：

```
Available programs:
  error (size: 10500 bytes)
  hello (size: 9984 bytes)
  fibonacci (size: 11240 bytes)    ← 新添加的程序
  count (size: 10324 bytes)
  test (size: 10552 bytes)
```

执行时会按顺序运行所有程序，包括新添加的程序。

## 故障排除

- **编译错误**：检查语法和包含的头文件
- **程序不显示**：确认已修改 Makefile 并重新编译
- **运行时错误**：检查是否调用了 `exit(0)`
- **程序顺序错误**：检查 `USER_SRCS` 中的顺序

通过遵循这些简单步骤，您可以轻松地为 MyOS 批处理系统添加新的用户程序。

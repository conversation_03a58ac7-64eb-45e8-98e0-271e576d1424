#include "user.h"

// 系统调用包装函数

int exit(int status)
{
    int ret;
    asm volatile("int $0x40" : "=a"(ret) : "a"(SYS_exit), "b"(status));
    return ret;
}

int write(int fd, const char *buf, int count)
{
    int ret;
    asm volatile("int $0x40" : "=a"(ret) : "a"(SYS_write), "b"(fd), "c"(buf), "d"(count));
    return ret;
}

int read(int fd, char *buf, int count)
{
    int ret;
    asm volatile("int $0x40" : "=a"(ret) : "a"(SYS_read), "b"(fd), "c"(buf), "d"(count));
    return ret;
}

int getpid(void)
{
    int ret;
    asm volatile("int $0x40" : "=a"(ret) : "a"(SYS_getpid));
    return ret;
}

// 用户库函数

int strlen(const char *s)
{
    int len = 0;
    while (s[len])
        len++;
    return len;
}

void strcpy(char *dst, const char *src)
{
    while ((*dst++ = *src++))
        ;
}

// 简化的printf实现
void printf(const char *fmt, ...)
{
    // 简化版：只支持字符串输出
    write(1, fmt, strlen(fmt));
}

// 数字转字符串
void itoa(int num, char *str)
{
    if (num == 0)
    {
        str[0] = '0';
        str[1] = '\0';
        return;
    }

    int i = 0;
    int temp = num;

    // 计算位数
    while (temp > 0)
    {
        temp /= 10;
        i++;
    }

    str[i] = '\0';

    // 转换数字
    while (num > 0)
    {
        str[--i] = '0' + (num % 10);
        num /= 10;
    }
}

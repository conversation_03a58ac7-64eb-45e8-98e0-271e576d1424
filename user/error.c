#include "user.h"

int main(void)
{
    printf("Hello from Error Test Program!\n");
    printf("This program will demonstrate error handling in batch processing.\n");

    int pid = getpid();
    printf("PID: ");
    char pid_str[10];
    itoa(pid, pid_str);
    printf(pid_str);
    printf("\n");

    printf("Testing different types of errors...\n");

    // 测试1: 执行无效指令
    printf("Test 1: Invalid instruction\n");
    printf("About to execute an invalid instruction...\n");

    // 使用内联汇编执行一个无效指令
    printf("Executing invalid instruction...\n");

    // 这里会触发无效指令异常
    __asm__ volatile("ud2"); // ud2 是一个专门用来触发无效指令异常的指令

    // 下面的代码不应该被执行到
    printf("This line should not be executed!\n");

    printf("Error program completed (this should not be printed)!\n");

    exit(0);
    return 0;
}

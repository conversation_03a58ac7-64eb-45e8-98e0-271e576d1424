#include "user.h"

int main(void)
{
    printf("Hello from User Program 2!\n");
    printf("PID: ");

    int pid = getpid();
    char pid_str[10];
    itoa(pid, pid_str);
    printf(pid_str);
    printf("\n");

    // 简单的计数循环
    printf("Counting from 1 to 5: ");
    int i;
    for (i = 1; i <= 5; i++)
    {
        char num_str[2];
        num_str[0] = '0' + i;
        num_str[1] = '\0';
        printf(num_str);
        printf(" ");
    }
    printf("\n");

    printf("Count program completed!\n");

    exit(0);
    return 0;
}

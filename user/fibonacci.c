#include "user.h"

int fibonacci(int n)
{
    if (n <= 1) return n;
    return fibonacci(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2);
}

int main(void)
{
    printf("Fibonacci Calculator Program\n");
    
    int pid = getpid();
    printf("PID: ");
    char pid_str[10];
    itoa(pid, pid_str);
    printf(pid_str);
    printf("\n");
    
    printf("Calculating Fibonacci sequence:\n");
    
    for (int i = 0; i < 10; i++) {
        int fib = fibonacci(i);
        printf("F(");
        char i_str[10];
        itoa(i, i_str);
        printf(i_str);
        printf(") = ");
        char fib_str[10];
        itoa(fib, fib_str);
        printf(fib_str);
        printf("\n");
    }
    
    printf("Fibonacci program completed!\n");
    exit(0);
    return 0;
}

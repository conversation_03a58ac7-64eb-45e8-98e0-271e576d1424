#ifndef _ELF_H_
#define _ELF_H_

// ELF文件格式

#define ELF_MAGIC 0x464C457FU  // "\x7FELF"

// ELF文件头
struct elfhdr {
  uint magic;  // 必须等于ELF_MAGIC
  uchar elf[12];
  ushort type;
  ushort machine;
  uint version;
  uint entry;  // 程序入口点
  uint phoff;  // 程序头表偏移
  uint shoff;  // 节头表偏移
  uint flags;
  ushort ehsize;
  ushort phentsize;
  ushort phnum;  // 程序头表条目数
  ushort shentsize;
  ushort shnum;
  ushort shstrndx;
};

// 程序节头
struct proghdr {
  uint type;
  uint off;     // 文件中的偏移
  uint vaddr;   // 虚拟地址
  uint paddr;   // 物理地址
  uint filesz;  // 文件中的大小
  uint memsz;   // 内存中的大小
  uint flags;
  uint align;
};

// 程序节头类型的值
#define ELF_PROG_LOAD           1

// 程序节头标志位
#define ELF_PROG_FLAG_EXEC      1
#define ELF_PROG_FLAG_WRITE     2
#define ELF_PROG_FLAG_READ      4

#endif // _ELF_H_

#ifndef _MMU_H_
#define _MMU_H_

// 内存管理单元定义

// CR0寄存器标志位
#define CR0_PE 0x00000001 // 保护模式启用
#define CR0_WP 0x00010000 // 写保护
#define CR0_PG 0x80000000 // 分页启用

// 页目录和页表常量
#define NPDENTRIES 1024 // 每个页目录的条目数
#define NPTENTRIES 1024 // 每个页表的条目数
#define PGSIZE 4096     // 页大小

// 页表/目录项标志位
#define PTE_P 0x001  // 存在
#define PTE_W 0x002  // 可写
#define PTE_U 0x004  // 用户可访问
#define PTE_PS 0x080 // 页大小

// 地址转换宏
#define PGROUNDUP(sz) (((sz) + PGSIZE - 1) & ~(PGSIZE - 1))
#define PGROUNDDOWN(a) (((a)) & ~(PGSIZE - 1))

// 内核栈大小
#define KSTACKSIZE 4096

#ifndef __ASSEMBLER__
// 页表项
typedef uint pte_t;

// 页目录项
typedef uint pde_t;
#endif

#endif // _MMU_H_

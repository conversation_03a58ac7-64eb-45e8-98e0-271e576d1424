#ifndef _UART_H_
#define _UART_H_

#include "types.h"

// 串行端口定义
#define COM1    0x3f8

// 串行端口寄存器
#define UART_RX         0       // 接收缓冲区 (读)
#define UART_TX         0       // 发送缓冲区 (写)
#define UART_DLL        0       // 除数锁存器低字节 (写)
#define UART_DLM        1       // 除数锁存器高字节 (写)
#define UART_IER        1       // 中断使能寄存器 (读/写)
#define UART_FCR        2       // FIFO控制寄存器 (写)
#define UART_ISR        2       // 中断状态寄存器 (读)
#define UART_LCR        3       // 线路控制寄存器 (读/写)
#define UART_MCR        4       // 调制解调器控制寄存器 (读/写)
#define UART_LSR        5       // 线路状态寄存器 (读)
#define UART_MSR        6       // 调制解调器状态寄存器 (读)
#define UART_SCR        7       // 刮擦寄存器 (读/写)

// 线路状态寄存器位
#define UART_LSR_DR     0x01    // 数据就绪
#define UART_LSR_OE     0x02    // 溢出错误
#define UART_LSR_PE     0x04    // 奇偶校验错误
#define UART_LSR_FE     0x08    // 帧错误
#define UART_LSR_BI     0x10    // 中断信号
#define UART_LSR_THRE   0x20    // 发送保持寄存器空
#define UART_LSR_TEMT   0x40    // 发送器空
#define UART_LSR_RXFE   0x80    // 接收FIFO错误

// 函数声明
void uartinit(void);            // 初始化串行端口
void uartputc(int c);           // 输出一个字符到串行端口
int uartgetc(void);             // 从串行端口读取一个字符

#endif // _UART_H_

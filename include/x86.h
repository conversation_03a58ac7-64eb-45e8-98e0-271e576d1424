#ifndef _X86_H_
#define _X86_H_

#include "types.h"

// I/O端口操作
static inline uchar
inb(ushort port)
{
  uchar data;
  asm volatile("in %1,%0" : "=a"(data) : "d"(port));
  return data;
}

static inline void
outb(ushort port, uchar data)
{
  asm volatile("out %0,%1" : : "a"(data), "d"(port));
}

static inline void
outw(ushort port, ushort data)
{
  asm volatile("out %0,%1" : : "a"(data), "d"(port));
}

// 加载全局描述符表寄存器
static inline void
lgdt(void *p)
{
  asm volatile("lgdt (%0)" : : "r"(p));
}

// 加载中断描述符表寄存器
static inline void
lidt(void *p)
{
  asm volatile("lidt (%0)" : : "r"(p));
}

// 加载任务寄存器
static inline void
ltr(ushort sel)
{
  asm volatile("ltr %0" : : "r"(sel));
}

// 加载CR0寄存器
static inline uint
rcr0(void)
{
  uint val;
  asm volatile("movl %%cr0,%0" : "=r"(val));
  return val;
}

static inline void
lcr0(uint val)
{
  asm volatile("movl %0,%%cr0" : : "r"(val));
}

// 启用中断
static inline void
sti(void)
{
  asm volatile("sti");
}

// 禁用中断
static inline void
cli(void)
{
  asm volatile("cli");
}

// 从端口读取多个双字
static inline void
insl(int port, void *addr, int cnt)
{
  asm volatile("cld; rep insl" : "=D"(addr), "=c"(cnt) : "d"(port), "0"(addr), "1"(cnt) : "memory", "cc");
}

#endif // _X86_H_

#ifndef _TRAP_H_
#define _TRAP_H_

#include "types.h"

// 中断向量号
#define T_DIVIDE 0   // 除零错误
#define T_DEBUG 1    // 调试异常
#define T_NMI 2      // 不可屏蔽中断
#define T_BRKPT 3    // 断点
#define T_OFLOW 4    // 溢出
#define T_BOUND 5    // 边界检查
#define T_ILLOP 6    // 非法操作码
#define T_DEVICE 7   // 设备不可用
#define T_DBLFLT 8   // 双重故障
#define T_TSS 10     // 无效TSS
#define T_SEGNP 11   // 段不存在
#define T_STACK 12   // 栈故障
#define T_GPFLT 13   // 一般保护故障
#define T_PGFLT 14   // 页故障
#define T_FPERR 16   // 浮点错误
#define T_ALIGN 17   // 对齐检查
#define T_MCHK 18    // 机器检查
#define T_SIMDERR 19 // SIMD浮点错误

// 系统调用中断号
#define T_SYSCALL 64 // 系统调用

// 中断描述符类型
#define STS_IG32 0xE // 32位中断门
#define STS_TG32 0xF // 32位陷阱门

// 特权级
#define DPL_USER 0x3   // 用户特权级
#define DPL_KERNEL 0x0 // 内核特权级

// 中断栈帧结构
struct trapframe
{
    // 由硬件推入的寄存器
    uint edi;
    uint esi;
    uint ebp;
    uint oesp; // 无用的esp
    uint ebx;
    uint edx;
    uint ecx;
    uint eax;

    // 中断号和错误码
    uint trapno;
    uint err;

    // 由硬件推入的
    uint eip;
    uint cs;
    uint eflags;

    // 如果特权级切换，还会有以下字段
    uint esp;
    uint ss;
};

// 中断描述符表项
struct gatedesc
{
    uint off_15_0 : 16;  // 偏移量低16位
    uint cs : 16;        // 代码段选择子
    uint args : 5;       // # args, 0 for interrupt/trap gates
    uint rsv1 : 3;       // 保留位
    uint type : 4;       // 类型(STS_{IG32,TG32})
    uint s : 1;          // 必须为0 (系统)
    uint dpl : 2;        // 描述符特权级
    uint p : 1;          // 存在位
    uint off_31_16 : 16; // 偏移量高16位
};

// 函数声明
void trap_init(void);
void trap_handler(struct trapframe *tf);

#endif // _TRAP_H_

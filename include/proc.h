#ifndef _PROC_H_
#define _PROC_H_

#include "types.h"
#include "memlayout.h"

// 进程状态
enum procstate
{
    UNUSED,
    EMBRYO,
    SLEEPING,
    RUNNABLE,
    RUNNING,
    ZOMBIE
};

// 简化的进程控制块
struct proc
{
    uint pid;             // 进程ID
    enum procstate state; // 进程状态
    uint entry;           // 程序入口地址
    uint size;            // 程序大小
    uint stack_base;      // 用户栈基地址
    uint stack_size;      // 用户栈大小
    char name[16];        // 进程名称

    // 上下文保存（简化版）
    struct context
    {
        uint eip;                // 指令指针
        uint esp;                // 栈指针
        uint ebp;                // 基址指针
        uint eax, ebx, ecx, edx; // 通用寄存器
        uint esi, edi;           // 索引寄存器
        uint eflags;             // 标志寄存器
    } context;
};

// 全局进程表
extern struct proc procs[MAXPROGS];
extern struct proc *current_proc;

// 进程管理函数
void proc_init(void);
struct proc *proc_alloc(void);
void proc_free(struct proc *p);
int proc_load(struct proc *p, void *binary, uint size);
void proc_run(struct proc *p);
void proc_exit(int status);

#endif // _PROC_H_

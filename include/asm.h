#ifndef _ASM_H_
#define _ASM_H_

// 段描述符类型位
#define STA_X     0x8       // 可执行段
#define STA_W     0x2       // 可写段
#define STA_R     0x2       // 可读段

// 段选择子
#define SEG_KCODE 1  // 内核代码
#define SEG_KDATA 2  // 内核数据

#ifndef __ASSEMBLER__
// 段描述符
struct segdesc {
  uint lim_15_0 : 16;  // 段限长低16位
  uint base_15_0 : 16; // 段基址低16位
  uint base_23_16 : 8; // 段基址中8位
  uint type : 4;       // 段类型
  uint s : 1;          // 0 = 系统, 1 = 应用
  uint dpl : 2;        // 描述符特权级
  uint p : 1;          // 存在位
  uint lim_19_16 : 4;  // 段限长高4位
  uint avl : 1;        // 保留位
  uint rsv1 : 1;       // 保留位
  uint db : 1;         // 0 = 16位段, 1 = 32位段
  uint g : 1;          // 粒度: 0 = 1字节, 1 = 4KB
  uint base_31_24 : 8; // 段基址高8位
};
#endif

// 汇编代码使用的段描述符格式
#define SEG_NULLASM                                             \
        .word 0, 0;                                             \
        .byte 0, 0, 0, 0

#define SEG_ASM(type,base,lim)                                  \
        .word (((lim) >> 12) & 0xffff), ((base) & 0xffff);      \
        .byte (((base) >> 16) & 0xff), (0x90 | (type)),         \
                (0xC0 | (((lim) >> 28) & 0xf)), (((base) >> 24) & 0xff)

#endif // _ASM_H_

#ifndef _CONSOLE_H_
#define _CONSOLE_H_

#include "types.h"

#define CRTPORT 0x3d4
#define CRT_ROWS 25
#define CRT_COLS 80
#define CRT_SIZE (CRT_ROWS * CRT_COLS)

// VGA显示缓冲区的颜色属性
#define BLACK         0x0
#define BLUE          0x1
#define GREEN         0x2
#define CYAN          0x3
#define RED           0x4
#define MAGENTA       0x5
#define BROWN         0x6
#define LIGHT_GREY    0x7
#define DARK_GREY     0x8
#define LIGHT_BLUE    0x9
#define LIGHT_GREEN   0xA
#define LIGHT_CYAN    0xB
#define LIGHT_RED     0xC
#define LIGHT_MAGENTA 0xD
#define LIGHT_BROWN   0xE
#define WHITE         0xF

// 默认的文本颜色
#define DEFAULT_COLOR (WHITE << 4 | LIGHT_GREY)

#endif // _CONSOLE_H_

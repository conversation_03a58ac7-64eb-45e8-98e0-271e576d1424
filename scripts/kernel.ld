/* 内核链接脚本 */
ENTRY(_start)

SECTIONS
{
  /* 链接地址 - 使用1MB位置 */
  . = 1M;

  /* Multiboot 头必须是第一个 */
  .text BLOCK(4K) : ALIGN(4K)
  {
    *(.multiboot)
    *(.text)
  }

  /* 只读数据 */
  .rodata BLOCK(4K) : ALIGN(4K)
  {
    *(.rodata)
  }

  /* 读写数据 (已初始化) */
  .data BLOCK(4K) : ALIGN(4K)
  {
    *(.data)
  }

  /* 读写数据 (未初始化) 和栈 */
  .bss BLOCK(4K) : ALIGN(4K)
  {
    *(COMMON)
    *(.bss)
    *(.bootstrap_stack)
  }
}

#!/usr/bin/env python3
"""
生成用户程序镜像的工具
将编译好的用户程序ELF文件转换为C数组，嵌入到内核中
"""

import sys
import os

def file_to_c_array(filename, array_name):
    """将文件转换为C数组"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()

        # 生成C数组
        c_code = f"// Generated from {filename}\n"
        c_code += f"static unsigned char {array_name}[] = {{\n"

        for i, byte in enumerate(data):
            if i % 16 == 0:
                c_code += "    "
            c_code += f"0x{byte:02x},"
            if i % 16 == 15:
                c_code += "\n"
            else:
                c_code += " "

        if len(data) % 16 != 0:
            c_code += "\n"

        c_code += "};\n"
        c_code += f"#define {array_name}_size {len(data)}\n\n"

        return c_code

    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        return ""

def main():
    if len(sys.argv) < 3:
        print("Usage: mkuserimg.py <output.c> <prog1.elf> [prog2.elf] ...")
        sys.exit(1)

    output_file = sys.argv[1]
    input_files = sys.argv[2:]

    # 生成C代码
    c_code = "#include \"../include/types.h\"\n"
    c_code += "#include \"../include/defs.h\"\n"
    c_code += "#include \"../include/proc.h\"\n"
    c_code += "#include \"../include/memlayout.h\"\n\n"

    # 为每个用户程序生成数组
    prog_names = []
    for i, filename in enumerate(input_files):
        if not os.path.exists(filename):
            print(f"Warning: {filename} does not exist, skipping")
            continue

        basename = os.path.basename(filename).replace('.', '_')
        array_name = f"user_prog_{basename}"
        prog_names.append((basename.replace('_elf', ''), array_name))

        c_code += file_to_c_array(filename, array_name)

    # 生成程序表
    c_code += "// 程序表\n"
    c_code += "struct {\n"
    c_code += "    char *name;\n"
    c_code += "    void *binary;\n"
    c_code += "    uint size;\n"
    c_code += "} programs[] = {\n"

    for name, array_name in prog_names:
        c_code += f"    {{\"{name}\", {array_name}, {array_name}_size}},\n"

    c_code += "    {0, 0, 0}  // 结束标记\n"
    c_code += "};\n\n"

    # 生成初始化和运行函数
    c_code += """// 初始化批处理系统
void batch_init(void)
{
    cprintf("Batch processing system initialized\\n");
    cprintf("Available programs:\\n");

    int i;
    for (i = 0; programs[i].name != 0; i++) {
        cprintf("  %s (size: %d bytes)\\n", programs[i].name, programs[i].size);
    }
}

// 运行批处理系统
void batch_run(void)
{
    cprintf("\\n=== Starting Batch Processing ===\\n");

    int i;
    for (i = 0; programs[i].name != 0; i++) {
        cprintf("\\n--- Running program: %s ---\\n", programs[i].name);

        // 分配进程
        struct proc *p = proc_alloc();
        if (p == 0) {
            cprintf("Failed to allocate process for %s\\n", programs[i].name);
            continue;
        }

        // 设置进程名称
        int j;
        for (j = 0; j < 15 && programs[i].name[j]; j++) {
            p->name[j] = programs[i].name[j];
        }
        p->name[j] = '\\0';

        // 加载程序
        if (proc_load(p, programs[i].binary, programs[i].size) < 0) {
            cprintf("Failed to load program %s\\n", programs[i].name);
            proc_free(p);
            continue;
        }

        // 运行程序
        proc_run(p);

        // 清理进程
        proc_free(p);

        cprintf("--- Program %s completed ---\\n", programs[i].name);
    }

    cprintf("\\n=== Batch Processing Completed ===\\n");
}
"""

    # 写入输出文件
    try:
        with open(output_file, 'w') as f:
            f.write(c_code)
        print(f"Generated {output_file} with {len(prog_names)} programs")
    except Exception as e:
        print(f"Error writing {output_file}: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

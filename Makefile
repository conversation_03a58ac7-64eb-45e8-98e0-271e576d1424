# 交叉编译工具链
CC = gcc
AS = gas
LD = ld
OBJCOPY = objcopy
OBJDUMP = objdump

# 目录结构
BOOT_DIR = boot
KERNEL_DIR = kernel
DRIVERS_DIR = drivers
INCLUDE_DIR = include
SCRIPTS_DIR = scripts
BUILD_DIR = build

# 编译标志
CFLAGS = -fno-pic -static -fno-builtin -fno-strict-aliasing -O2 -Wall -MD -ggdb -m32 -Werror -fno-omit-frame-pointer
CFLAGS += -fno-stack-protector -I$(INCLUDE_DIR)
ASFLAGS = -m32 -gdwarf-2 -Wa,-divide -I$(INCLUDE_DIR)
# 链接标志
LDFLAGS = -m elf_i386

# 源文件
BOOT_SRCS = $(BOOT_DIR)/bootasm.S $(BOOT_DIR)/bootmain.c
KERNEL_SRCS = $(KERNEL_DIR)/entry.S $(KERNEL_DIR)/main.c $(KERNEL_DIR)/proc.c $(KERNEL_DIR)/syscall.c $(KERNEL_DIR)/trap.c $(KERNEL_DIR)/trapasm.S $(KERNEL_DIR)/elf.c $(KERNEL_DIR)/setjmp.S
DRIVERS_SRCS = $(DRIVERS_DIR)/console.c $(DRIVERS_DIR)/uart.c

# 用户程序源文件
USER_DIR = user
USER_SRCS = $(USER_DIR)/error.c $(USER_DIR)/hello.c $(USER_DIR)/fibonacci.c $(USER_DIR)/count.c $(USER_DIR)/test.c
USER_LIBS = $(USER_DIR)/ulib.c $(USER_DIR)/usys.S

# 目标文件
BOOT_OBJS = $(patsubst $(BOOT_DIR)/%.S,$(BUILD_DIR)/%.o,$(patsubst $(BOOT_DIR)/%.c,$(BUILD_DIR)/%.o,$(BOOT_SRCS)))
KERNEL_OBJS = $(patsubst $(KERNEL_DIR)/%.S,$(BUILD_DIR)/%.o,$(patsubst $(KERNEL_DIR)/%.c,$(BUILD_DIR)/%.o,$(KERNEL_SRCS)))
DRIVERS_OBJS = $(patsubst $(DRIVERS_DIR)/%.c,$(BUILD_DIR)/%.o,$(DRIVERS_SRCS))

# 用户程序目标文件
USER_PROGS = $(patsubst $(USER_DIR)/%.c,$(BUILD_DIR)/%.elf,$(USER_SRCS))
USER_LIB_OBJS = $(patsubst $(USER_DIR)/%.c,$(BUILD_DIR)/user_%.o,$(USER_LIBS)) $(patsubst $(USER_DIR)/%.S,$(BUILD_DIR)/user_%.o,$(USER_LIBS))

# 所有内核对象
KERNEL_ALL_OBJS = $(KERNEL_OBJS) $(DRIVERS_OBJS) $(BUILD_DIR)/batch.o

# 默认目标
all: directories user-progs myos.img

# 创建必要的目录
directories:
	@mkdir -p $(BUILD_DIR)

# 内核目标
kernel: $(KERNEL_ALL_OBJS)
	$(LD) $(LDFLAGS) -T $(SCRIPTS_DIR)/kernel.ld -o $(BUILD_DIR)/kernel $(KERNEL_ALL_OBJS)
	$(OBJDUMP) -S $(BUILD_DIR)/kernel > $(BUILD_DIR)/kernel.asm

$(BUILD_DIR)/kernel.bin: kernel
	$(OBJCOPY) -S -O binary $(BUILD_DIR)/kernel $(BUILD_DIR)/kernel.bin

# 引导加载程序
bootblock: $(BOOT_OBJS)
	$(LD) $(LDFLAGS) -N -e start -Ttext 0x7C00 -o $(BUILD_DIR)/bootblock.o $(BOOT_OBJS)
	$(OBJDUMP) -S $(BUILD_DIR)/bootblock.o > $(BUILD_DIR)/bootblock.asm
	$(OBJCOPY) -S -O binary -j .text $(BUILD_DIR)/bootblock.o $(BUILD_DIR)/bootblock
	$(BOOT_DIR)/sign.pl $(BUILD_DIR)/bootblock

# 磁盘镜像
myos.img: bootblock kernel
	dd if=/dev/zero of=myos.img count=10000
	dd if=$(BUILD_DIR)/bootblock of=myos.img conv=notrunc
	dd if=$(BUILD_DIR)/kernel of=myos.img seek=1 conv=notrunc

# 在QEMU中运行
qemu: myos.img
	qemu-system-i386 -drive file=myos.img,index=0,media=disk,format=raw -m 512 -nographic -monitor null -serial stdio

# 在QEMU中调试
qemu-gdb: $(BUILD_DIR)/kernel
	qemu-system-i386 -kernel $(BUILD_DIR)/kernel -m 512 -nographic -monitor null -serial stdio -S -gdb tcp::26000

# 清理
clean:
	rm -rf $(BUILD_DIR) *.img

# 编译规则
$(BUILD_DIR)/%.o: $(BOOT_DIR)/%.c
	$(CC) $(CFLAGS) -c -o $@ $<

$(BUILD_DIR)/%.o: $(BOOT_DIR)/%.S
	$(CC) $(ASFLAGS) -c -o $@ $<

$(BUILD_DIR)/%.o: $(KERNEL_DIR)/%.c
	$(CC) $(CFLAGS) -c -o $@ $<

$(BUILD_DIR)/%.o: $(KERNEL_DIR)/%.S
	$(CC) $(ASFLAGS) -c -o $@ $<

$(BUILD_DIR)/%.o: $(DRIVERS_DIR)/%.c
	$(CC) $(CFLAGS) -c -o $@ $<

# 用户程序编译规则

# 编译用户库
$(BUILD_DIR)/user_ulib.o: $(USER_DIR)/ulib.c
	$(CC) $(CFLAGS) -c -o $@ $<

$(BUILD_DIR)/user_usys.o: $(USER_DIR)/usys.S
	$(CC) $(ASFLAGS) -c -o $@ $<

# 编译用户程序
$(BUILD_DIR)/%.elf: $(USER_DIR)/%.c $(BUILD_DIR)/user_ulib.o $(BUILD_DIR)/user_usys.o
	$(CC) $(CFLAGS) -c -o $(BUILD_DIR)/$*.o $<
	$(LD) $(LDFLAGS) -T $(USER_DIR)/user.ld -o $@ $(BUILD_DIR)/user_usys.o $(BUILD_DIR)/$*.o $(BUILD_DIR)/user_ulib.o

# 生成用户程序镜像
user-progs: $(USER_PROGS)
	python3 $(SCRIPTS_DIR)/mkuserimg.py $(BUILD_DIR)/batch.c $(USER_PROGS)

# 编译生成的batch.c
$(BUILD_DIR)/batch.o: $(BUILD_DIR)/batch.c
	$(CC) $(CFLAGS) -c -o $@ $<

# 包含依赖文件
-include $(BUILD_DIR)/*.d

#include "asm.h"

# 引导加载程序从实模式启动，进入保护模式，然后跳转到C代码

.code16                       # 16位代码
.globl start
start:
  cli                         # 禁用中断

  # 启用A20线
  movw    $0x2401, %ax       # A20快速启用
  int     $0x15
  jnc     1f
  movb    $0xd1, %al         # A20备用方法：通过键盘控制器
  outb    %al, $0x64
  movb    $0xdf, %al
  outb    %al, $0x60
1:

  # 设置段寄存器为0
  xorw    %ax, %ax
  movw    %ax, %ds
  movw    %ax, %es
  movw    %ax, %ss

  # 加载GDT
  lgdt    gdtdesc

  # 切换到保护模式
  movl    %cr0, %eax
  orl     $0x1, %eax
  movl    %eax, %cr0

  # 长跳转到32位代码
  ljmp    $(SEG_KCODE<<3), $start32

.code32  # 告诉汇编器生成32位代码
start32:
  # 设置保护模式下的段寄存器
  movw    $(SEG_KDATA<<3), %ax
  movw    %ax, %ds
  movw    %ax, %es
  movw    %ax, %ss
  movw    $0, %ax
  movw    %ax, %fs
  movw    %ax, %gs

  # 设置栈指针
  movl    $start, %esp
  
  # 调用C代码
  call    bootmain

  # 如果bootmain返回，进入无限循环
spin:
  jmp     spin

# GDT
.p2align 2                    # 强制4字节对齐
gdt:
  SEG_NULLASM                 # 空段
  SEG_ASM(STA_X|STA_R, 0x0, 0xffffffff)   # 代码段
  SEG_ASM(STA_W, 0x0, 0xffffffff)         # 数据段

gdtdesc:
  .word   (gdtdesc - gdt - 1)             # GDT大小
  .long   gdt                             # GDT地址
